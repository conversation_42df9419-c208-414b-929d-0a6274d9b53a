import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:vinta_shared_commons/constants/spaces.dart';

import '../../../services/clients/rest/apis/users/dtos.dart';
import '../../../services/roles/models.dart';
import '../../../services/users/models.dart';
import '../../constants/colors.dart';

class AddUserModal extends StatefulWidget {
  final Function(CreateUserRequest) onSave;
  final List<RoleModel> availableRoles;
  final String tenantId;

  const AddUserModal({
    super.key,
    required this.onSave,
    required this.availableRoles,
    required this.tenantId,
  });

  @override
  State<AddUserModal> createState() => _AddUserModalState();
}

class _AddUserModalState extends State<AddUserModal> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _emailController = TextEditingController();
  final _phoneController = TextEditingController();

  UserType _selectedUserType = UserType.CUSTOMER;
  final Set<String> _selectedRoleIds = <String>{};
  bool _isLoading = false;

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Container(
        width: 500,
        padding: const EdgeInsets.all(24),
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Add New User',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.w600,
                      color: AppColors.colorPrimary01,
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: Icon(Icons.close),
                  ),
                ],
              ),
              AppSpaces.spaceH24,

              // Full Name Field
              TextFormField(
                controller: _nameController,
                decoration: InputDecoration(
                  labelText: 'Full Name *',
                  border: OutlineInputBorder(),
                  contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 16),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Full name is required';
                  }
                  return null;
                },
              ),
              AppSpaces.spaceH16,

              // Email Field
              TextFormField(
                controller: _emailController,
                decoration: InputDecoration(
                  labelText: 'Email *',
                  border: OutlineInputBorder(),
                  contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 16),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Email is required';
                  }
                  if (!GetUtils.isEmail(value.trim())) {
                    return 'Please enter a valid email';
                  }
                  return null;
                },
              ),
              AppSpaces.spaceH16,

              // Phone Number Field
              TextFormField(
                controller: _phoneController,
                decoration: InputDecoration(
                  labelText: 'Phone Number',
                  border: OutlineInputBorder(),
                  contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 16),
                ),
              ),
              AppSpaces.spaceH16,

              // User Type Dropdown
              DropdownButtonFormField<UserType>(
                value: _selectedUserType,
                decoration: InputDecoration(
                  labelText: 'User Type *',
                  border: OutlineInputBorder(),
                  contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 16),
                ),
                items: UserType.values.map((type) {
                  return DropdownMenuItem<UserType>(
                    value: type,
                    child: Text(type.name),
                  );
                }).toList(),
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      _selectedUserType = value;
                    });
                  }
                },
              ),
              AppSpaces.spaceH16,

              // User Roles Section
              Text(
                'User Roles *',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: AppColors.colorPrimary01,
                ),
              ),
              AppSpaces.spaceH8,
              Container(
                height: 150,
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey.shade400),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: widget.availableRoles.isEmpty
                    ? Center(
                        child: Text(
                          'No roles available',
                          style: TextStyle(color: Colors.grey.shade600),
                        ),
                      )
                    : ListView.builder(
                        itemCount: widget.availableRoles.length,
                        itemBuilder: (context, index) {
                          final role = widget.availableRoles[index];
                          return CheckboxListTile(
                            title: Text(role.title),
                            subtitle: role.description != null
                                ? Text(role.description!, style: TextStyle(fontSize: 12))
                                : null,
                            value: _selectedRoleIds.contains(role.id),
                            onChanged: (bool? value) {
                              setState(() {
                                if (value == true) {
                                  _selectedRoleIds.add(role.id);
                                } else {
                                  _selectedRoleIds.remove(role.id);
                                }
                              });
                            },
                          );
                        },
                      ),
              ),
              if (_selectedRoleIds.isEmpty)
                Padding(
                  padding: const EdgeInsets.only(top: 8),
                  child: Text(
                    'Please select at least one role',
                    style: TextStyle(color: Colors.red.shade600, fontSize: 12),
                  ),
                ),
              AppSpaces.spaceH24,

              // Action Buttons
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
                    child: Text('Cancel'),
                  ),
                  AppSpaces.spaceW16,
                  ElevatedButton(
                    onPressed: _isLoading ? null : _handleSave,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.colorPrimary01,
                      foregroundColor: Colors.white,
                      padding: EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                    ),
                    child: _isLoading
                        ? SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                            ),
                          )
                        : Text('Save'),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _handleSave() {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_selectedRoleIds.isEmpty) {
      Get.snackbar(
        'Validation Error',
        'Please select at least one role',
        backgroundColor: Colors.red.shade100,
        colorText: Colors.red.shade800,
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    final request = CreateUserRequest(
      email: _emailController.text.trim(),
      fullName: _nameController.text.trim(),
      phoneNumber: _phoneController.text.trim().isEmpty ? null : _phoneController.text.trim(),
      userType: _selectedUserType,
      userRoles: _selectedRoleIds.map((roleId) => CreateUserRoleRequest(
        tenantId: widget.tenantId,
        roleId: roleId,
      )).toSet(),
    );

    widget.onSave(request);
  }
}
