import 'package:containerbase/commons/widgets/vinta_paging_datatable/models.dart';
import 'package:containerbase/services/users/requests.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../commons/widgets/add_user_modal/view.dart';
import '../../services/clients/rest/apis/users/dtos.dart';
import '../../services/roles/models.dart';
import '../../services/roles/service.dart';
import '../../services/tenant/service.dart';
import '../../services/users/models.dart';
import '../../services/users/service.dart';
import 'state.dart';

class AccountPageController extends GetxController {
  final state = AccountPageState();
  final _userService = Get.find<UserService>();
  final _roleService = Get.find<RoleService>();
  final _tenantService = Get.find<TenantService>();

  final availableRoles = <RoleModel>[].obs;
  final isLoadingRoles = false.obs;

  @override
  void onInit() {
    super.onInit();
    _loadAvailableRoles();
  }

  Future<PagingResponse<UserModel>> queryUsers(PageRequest<UserFilter?> pageRequest) {
    return _userService.queryUsers(pageRequest);
  }

  Future<void> _loadAvailableRoles() async {
    try {
      isLoadingRoles.value = true;
      final roles = await _roleService.getAllRoles();
      availableRoles.value = roles;
    } catch (e) {
      Get.snackbar(
        'Error',
        'Failed to load roles: ${e.toString()}',
        backgroundColor: Colors.red.shade100,
        colorText: Colors.red.shade800,
      );
    } finally {
      isLoadingRoles.value = false;
    }
  }

  void showAddUserModal() {
    Get.dialog(
      AddUserModal(
        availableRoles: availableRoles.value,
        tenantId: _tenantService.getCurrentTenant().id,
        onSave: _handleCreateUser,
      ),
      barrierDismissible: false,
    );
  }

  Future<void> _handleCreateUser(CreateUserRequest request) async {
    try {
      await _userService.createUser(request);
      Get.back(); // Close the modal
      Get.snackbar(
        'Success',
        'User created successfully',
        backgroundColor: Colors.green.shade100,
        colorText: Colors.green.shade800,
      );
      // Refresh the user list
      // You might want to trigger a refresh of the data table here
    } catch (e) {
      Get.snackbar(
        'Error',
        'Failed to create user: ${e.toString()}',
        backgroundColor: Colors.red.shade100,
        colorText: Colors.red.shade800,
      );
    }
  }
}
